<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx">
        <testCase name="RADraftHeader Basic Rendering renders the component without crashing" duration="42">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:25:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Basic Rendering renders breadcrumb navigation correctly" duration="7">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:37:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Basic Rendering renders tab buttons correctly" duration="6">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:56:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Basic Rendering applies correct container classes" duration="5">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:68:27)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Basic Rendering applies correct breadcrumb container classes" duration="6">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:80:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Basic Rendering applies correct tab container classes" duration="5">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:89:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Tab Functionality shows Risk Assessment tab as active when activeTab is 1" duration="7">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:100:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Tab Functionality shows RA Template tab as active when activeTab is 2" duration="6">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:112:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Tab Functionality calls setActiveTab with 1 when Risk Assessment tab is clicked" duration="6">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:127:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Tab Functionality calls setActiveTab with 2 when RA Template tab is clicked" duration="6">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:139:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Tab Functionality handles multiple tab clicks correctly" duration="5">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:149:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Button Properties applies correct variant to tab buttons" duration="6">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:170:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Button Properties applies draft-listing-tab class to both buttons" duration="6">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:182:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Edge Cases handles activeTab value of 0 correctly" duration="8">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:196:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Edge Cases handles activeTab value greater than 2 correctly" duration="4">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:208:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Edge Cases handles negative activeTab value correctly" duration="5">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:220:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Accessibility has proper button roles" duration="5">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:234:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Accessibility has proper link role for breadcrumb" duration="5">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:246:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Component Structure maintains proper DOM hierarchy" duration="5">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:257:27)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Component Structure contains both tabs within tab container" duration="5">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:274:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
        <testCase name="RADraftHeader Props Validation works with different setActiveTab functions" duration="5">
            <failure message="Error: useDataStoreContext must be used within a DataStoreProvider"><![CDATA[Error: useDataStoreContext must be used within a DataStoreProvider
    at useContextWrapper (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/hooks/useContextWrapper.ts:24:11)
    at useDataStoreContext (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/context/DataStoreProvider.tsx:84:20)
    at RADraftHeader (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/src/pages/RADrafts/RADraftHeader.tsx:17:26)
    at renderWithHooks (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:15486:18)
    at mountIndeterminateComponent (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:20103:13)
    at beginWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:21626:16)
    at beginWork$1 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:27465:14)
    at performUnitOfWork (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26599:12)
    at workLoopSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26505:5)
    at renderRootSync (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:26473:7)
    at recoverFromConcurrentError (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25889:20)
    at performConcurrentWorkOnRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom.development.js:25789:22)
    at flushActQueue (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2667:24)
    at act (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react/cjs/react.development.js:2582:11)
    at actWithWarning (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/react-dom/cjs/react-dom-test-utils.development.js:1740:10)
    at /Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/act-compat.js:63:25
    at renderRoot (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:159:26)
    at render (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@testing-library/react/dist/pure.js:246:10)
    at renderWithRouter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:16:18)
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx:293:7)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at TestRunner.runTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/index.js:101:12)
    at TestScheduler.scheduleTests (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/TestScheduler.js:333:13)
    at runJest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/runJest.js:404:19)
    at _run10000 (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:320:7)
    at runCLI (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/@jest/core/build/cli/index.js:173:3)
    at Object.run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-cli/build/cli/index.js:163:37)]]></failure>
        </testCase>
    </file>
</testExecutions>